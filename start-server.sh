#!/bin/bash

# HaoSurge Browser Project Launcher (Linux/Mac)
# 统一端口：8080

echo "========================================"
echo "   HaoSurge Browser Project Launcher"
echo "========================================"
echo

# 检查端口8080是否被占用
echo "Checking port 8080..."
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "Port 8080 is already in use. Stopping existing processes..."
    # 获取占用端口的进程ID并终止
    PIDS=$(lsof -Pi :8080 -sTCP:LISTEN -t)
    for PID in $PIDS; do
        echo "Stopping process $PID..."
        kill -9 $PID 2>/dev/null
    done
    sleep 2
fi

echo "Starting local server on port 8080..."
echo

# 优先使用Python HTTP服务器
if command -v python3 >/dev/null 2>&1; then
    echo "Using Python 3 HTTP server..."
    echo "Server URL: http://localhost:8080"
    echo
    echo "Available pages:"
    echo "- Main page: http://localhost:8080"
    echo "- Location test: http://localhost:8080/test-location.html"
    echo "- Function test: http://localhost:8080/功能验证.html"
    echo
    echo "Press Ctrl+C to stop the server"
    echo "========================================"
    python3 -m http.server 8080
elif command -v python >/dev/null 2>&1; then
    echo "Using Python HTTP server..."
    echo "Server URL: http://localhost:8080"
    echo
    echo "Available pages:"
    echo "- Main page: http://localhost:8080"
    echo "- Location test: http://localhost:8080/test-location.html"
    echo "- Function test: http://localhost:8080/功能验证.html"
    echo
    echo "Press Ctrl+C to stop the server"
    echo "========================================"
    python -m http.server 8080
elif command -v node >/dev/null 2>&1; then
    echo "Using Node.js HTTP server..."
    echo "Server URL: http://localhost:8080"
    echo
    echo "Available pages:"
    echo "- Main page: http://localhost:8080"
    echo "- Location test: http://localhost:8080/test-location.html"
    echo "- Function test: http://localhost:8080/功能验证.html"
    echo
    echo "Press Ctrl+C to stop the server"
    echo "========================================"
    npx http-server -p 8080 -c-1
else
    echo "Error: Neither Python nor Node.js is available"
    echo
    echo "Please install Python or Node.js to run the server"
    echo "Or open index.html directly in your browser"
    echo
    exit 1
fi
