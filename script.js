/**
 * HaoSurge 项目主JavaScript文件
 * 功能：控制LOGO启动动画、头像交互、菜单交互、登录表单交互、表单验证、社交登录、动态国旗图标、位置选择、语言翻译
 * 优化：模块化代码、增强交互效果、完善错误处理、提高性能、添加社交表单、密码登录、位置和语言模块
 * 日期：2025年5月2日
 */
document.addEventListener('DOMContentLoaded', () => {
    // 元素缓存，减少DOM查询
    const elements = {
        logoOverlay: document.getElementById('logoOverlay'),
        avatarToggle: document.getElementById('avatarToggle'),
        menuOverlay: document.getElementById('menuOverlay'),
        loginButton: document.getElementById('loginButton'),
        authOverlay: document.getElementById('authOverlay'),
        authBackdrop: document.getElementById('authBackdrop'),
        closeAuth: document.getElementById('closeAuth'),
        authTabs: document.querySelectorAll('.auth-tab'),
        authForms: document.querySelectorAll('.auth-form'),
        authLinks: document.querySelectorAll('.auth-link'),
        sendCodeButtons: document.querySelectorAll('.send-code'),
        socialButtons: document.querySelectorAll('.social-auth-button'),
        socialBackButton: document.querySelector('.social-back-button'),
        menuItems: document.querySelectorAll('.menu-item'),
        countryCodes: document.querySelectorAll('.country-code'),
        locationOverlay: document.getElementById('locationOverlay'),
        locationBack: document.getElementById('locationBack'),
        currentLocation: document.getElementById('currentLocation'),
        locationSelect: document.getElementById('locationSelect'),
        resetLocationBtn: document.getElementById('resetLocationBtn'),
        mainLocationDisplay: document.getElementById('mainLocationDisplay'),
        languageOverlay: document.getElementById('languageOverlay'),
        languageBack: document.getElementById('languageBack'),
        languageSelect: document.getElementById('languageSelect')
    };

    // 全局状态
    let isLoggedIn = false;
    let countdown = null;
    let lastActiveTab = 'phone';
    let touchStartX = 0;
    let touchEndX = 0;

    /**
     * 初始化LOGO启动动画
     */
    function initLogoAnimation() {
        if (!sessionStorage.getItem('hasVisited')) {
            setTimeout(() => {
                elements.logoOverlay.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                elements.logoOverlay.style.opacity = '0';
                elements.logoOverlay.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    elements.logoOverlay.style.display = 'none';
                }, 500);
                sessionStorage.setItem('hasVisited', 'true');
            }, 2000);
        } else {
            elements.logoOverlay.style.display = 'none';
        }
    }

    /**
     * 动态更新国旗图标
     * @param {HTMLSelectElement} select - 国家码下拉框
     */
    function updateCountryFlag(select) {
        const selectedOption = select.options[select.selectedIndex];
        const flagUrl = selectedOption.dataset.flag;
        const flagImg = select.parentElement.querySelector('.country-flag');
        if (flagImg && flagUrl) {
            flagImg.src = flagUrl;
            flagImg.alt = `Flag of ${selectedOption.text}`;
        }
    }

    /**
     * 初始化国旗图标
     */
    function initCountryFlags() {
        elements.countryCodes.forEach(select => {
            updateCountryFlag(select);
            select.addEventListener('change', () => updateCountryFlag(select));
        });
    }

    /**
     * 获取IP位置信息（智能自动显示真实IP和位置）
     */
    async function fetchIPLocation() {
        try {
            // 尝试多个IP位置API以提高成功率
            const apis = [
                {
                    url: 'https://ipapi.co/json/',
                    parser: (data) => ({
                        ip: data.ip,
                        country: data.country_name,
                        region: data.region,
                        city: data.city
                    })
                },
                {
                    url: 'https://ip-api.com/json/',
                    parser: (data) => ({
                        ip: data.query,
                        country: data.country,
                        region: data.regionName,
                        city: data.city
                    })
                },
                {
                    url: 'https://ipinfo.io/json',
                    parser: (data) => ({
                        ip: data.ip,
                        country: data.country,
                        region: data.region,
                        city: data.city
                    })
                }
            ];

            let locationData = null;

            // 依次尝试各个API
            for (const api of apis) {
                try {
                    const response = await fetch(api.url);
                    if (response.ok) {
                        const data = await response.json();
                        locationData = api.parser(data);
                        break;
                    }
                } catch (apiError) {
                    console.warn(`API ${api.url} 失败:`, apiError);
                    continue;
                }
            }

            if (locationData) {
                // 格式化位置信息：IP地址 | 国家省份-城市
                const formattedLocation = formatLocationDisplay(locationData);
                elements.currentLocation.innerHTML = formattedLocation;

                // 更新主页面位置显示
                updateMainLocationDisplay(locationData);

                // 存储位置数据供其他功能使用
                window.currentLocationData = locationData;
            } else {
                throw new Error('所有IP位置API都无法访问');
            }
        } catch (error) {
            elements.currentLocation.innerHTML = '<span style="color: #ff6b6b;">无法获取位置信息</span>';
            // 更新主页面显示错误状态
            if (elements.mainLocationDisplay) {
                elements.mainLocationDisplay.innerHTML = '<span style="color: #ff6b6b;">位置获取失败</span>';
            }
            console.error('获取IP位置失败:', error);
        }
    }

    /**
     * 更新主页面位置显示
     * @param {Object} locationData - 位置数据对象
     */
    function updateMainLocationDisplay(locationData) {
        if (!elements.mainLocationDisplay) return;

        const { ip, country, region, city } = locationData;

        // 中文国家名称映射
        const countryMap = {
            'China': '中国',
            'United States': '美国',
            'Japan': '日本',
            'South Korea': '韩国',
            'United Kingdom': '英国',
            'Germany': '德国',
            'France': '法国',
            'Canada': '加拿大',
            'Australia': '澳大利亚',
            'Singapore': '新加坡',
            'Hong Kong': '香港',
            'Taiwan': '台湾',
            'Macao': '澳门',
            'Russia': '俄罗斯',
            'India': '印度',
            'Brazil': '巴西',
            'Mexico': '墨西哥',
            'Thailand': '泰国',
            'Vietnam': '越南',
            'Malaysia': '马来西亚',
            'Indonesia': '印度尼西亚',
            'Philippines': '菲律宾'
        };

        const countryName = countryMap[country] || country;

        // 构建位置字符串，确保格式为"国家省份-城市"
        let locationParts = [countryName];

        // 添加省份/州/地区（如果存在且不同于国家名）
        if (region && region !== country && region !== countryName) {
            locationParts.push(region);
        }

        // 添加城市（如果存在且不同于地区名）
        if (city && city !== region && city !== country && city !== countryName) {
            locationParts.push(city);
        }

        const locationString = locationParts.join('-');

        // 更新主页面显示，格式：IP地址 位置信息
        elements.mainLocationDisplay.innerHTML = `
            <span class="ip-display">${ip}</span>
            <span class="location-display">${locationString}</span>
        `;
    }

    /**
     * 格式化位置显示信息
     * @param {Object} locationData - 位置数据对象
     * @returns {string} 格式化的位置字符串
     */
    function formatLocationDisplay(locationData) {
        const { ip, country, region, city } = locationData;

        // 中文国家名称映射（扩展版）
        const countryMap = {
            'China': '中国',
            'United States': '美国',
            'Japan': '日本',
            'South Korea': '韩国',
            'United Kingdom': '英国',
            'Germany': '德国',
            'France': '法国',
            'Canada': '加拿大',
            'Australia': '澳大利亚',
            'Singapore': '新加坡',
            'Hong Kong': '香港',
            'Taiwan': '台湾',
            'Macao': '澳门',
            'Russia': '俄罗斯',
            'India': '印度',
            'Brazil': '巴西',
            'Mexico': '墨西哥',
            'Thailand': '泰国',
            'Vietnam': '越南',
            'Malaysia': '马来西亚',
            'Indonesia': '印度尼西亚',
            'Philippines': '菲律宾',
            'Netherlands': '荷兰',
            'Italy': '意大利',
            'Spain': '西班牙',
            'Switzerland': '瑞士',
            'Sweden': '瑞典',
            'Norway': '挪威',
            'Denmark': '丹麦',
            'Finland': '芬兰'
        };

        // 获取中文国家名称，如果没有映射则使用原名称
        const countryName = countryMap[country] || country;

        // 构建位置字符串，确保格式为"国家省份-城市"
        let locationParts = [countryName];

        // 添加省份/州/地区（如果存在且不同于国家名）
        if (region && region !== country && region !== countryName) {
            locationParts.push(region);
        }

        // 添加城市（如果存在且不同于地区名）
        if (city && city !== region && city !== country && city !== countryName) {
            locationParts.push(city);
        }

        const locationString = locationParts.join('-');

        // 返回格式化的HTML字符串，显示IP地址和位置信息
        return `
            <div class="ip-location-info">
                <div class="ip-address">
                    <i class="ri-global-line"></i>
                    <span class="ip-text">${ip}</span>
                </div>
                <div class="location-text">
                    <i class="ri-map-pin-line"></i>
                    <span>${locationString}</span>
                </div>
            </div>
        `;
    }

    /**
     * 翻译文本（使用免费API：libretranslate.com）
     * @param {string} text - 要翻译的文本
     * @param {string} targetLang - 目标语言代码
     */
    async function translateText(text, targetLang) {
        try {
            const response = await fetch('https://libretranslate.com/translate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    q: text,
                    source: 'auto',
                    target: targetLang,
                    format: 'text'
                })
            });
            const data = await response.json();
            return data.translatedText;
        } catch (error) {
            console.error('翻译失败:', error);
            return text;
        }
    }

    /**
     * 更新页面语言
     * @param {string} lang - 语言代码
     */
    async function updateLanguage(lang) {
        const elementsToTranslate = document.querySelectorAll('[data-translate]');
        for (const element of elementsToTranslate) {
            const text = element.dataset.translate;
            const translated = await translateText(text, lang);
            element.textContent = translated;
        }
    }

    /**
     * 初始化位置和语言模块
     */
    function initLocationAndLanguage() {
        // 获取真实IP位置
        fetchIPLocation();

        // 位置选择器变化事件
        elements.locationSelect.addEventListener('change', () => {
            const selectedValue = elements.locationSelect.value;
            const selectedText = elements.locationSelect.options[elements.locationSelect.selectedIndex].text;

            // 创建虚拟位置显示（当用户手动选择位置时）
            const virtualLocationHTML = `
                <div class="ip-location-info" style="border: 2px dashed #1a73e8;">
                    <div class="location-text">
                        <i class="ri-map-pin-line"></i>
                        <span style="color: #1a73e8;">${selectedText} (手动选择)</span>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 8px; display: flex; align-items: center;">
                        <i class="ri-information-line" style="margin-right: 4px;"></i>
                        这是您手动选择的位置，非真实IP位置
                    </div>
                </div>
            `;

            elements.currentLocation.innerHTML = virtualLocationHTML;

            // 更新主页面显示
            if (elements.mainLocationDisplay) {
                elements.mainLocationDisplay.innerHTML = `
                    <span class="location-display" style="color: #1a73e8;">${selectedText} (手动选择)</span>
                `;
            }

            // 显示重置按钮
            if (elements.resetLocationBtn) {
                elements.resetLocationBtn.style.display = 'flex';
            }

            // 存储选择的位置信息
            window.selectedLocationData = {
                value: selectedValue,
                text: selectedText,
                isManual: true
            };

            console.log('用户选择位置:', selectedText);
        });

        // 重置位置按钮事件
        if (elements.resetLocationBtn) {
            elements.resetLocationBtn.addEventListener('click', () => {
                resetToRealLocation();
                elements.resetLocationBtn.style.display = 'none';
                elements.locationSelect.selectedIndex = 0;
            });
        }

        // 语言选择器变化事件
        elements.languageSelect.addEventListener('change', () => {
            const lang = elements.languageSelect.value;
            updateLanguage(lang);
        });
    }

    /**
     * 重置到真实IP位置
     */
    function resetToRealLocation() {
        if (window.currentLocationData) {
            const formattedLocation = formatLocationDisplay(window.currentLocationData);
            elements.currentLocation.innerHTML = formattedLocation;
            // 更新主页面显示
            updateMainLocationDisplay(window.currentLocationData);
            elements.locationSelect.selectedIndex = 0; // 重置选择器
        } else {
            fetchIPLocation(); // 重新获取
        }
    }

    /**
     * 切换菜单显示状态
     */
    function toggleMenu() {
        elements.avatarToggle.classList.toggle('active');
        elements.menuOverlay.classList.toggle('visible');
        elements.locationOverlay.classList.remove('visible');
        elements.languageOverlay.classList.remove('visible');
        document.body.style.overflow = elements.menuOverlay.classList.contains('visible') ? 'hidden' : '';
    }

    /**
     * 关闭菜单
     */
    function closeMenu() {
        elements.avatarToggle.classList.remove('active');
        elements.menuOverlay.classList.remove('visible');
        elements.locationOverlay.classList.remove('visible');
        elements.languageOverlay.classList.remove('visible');
        document.body.style.overflow = '';
    }

    /**
     * 打开登录表单
     */
    function openAuthForm() {
        elements.authOverlay.classList.add('visible');
        closeMenu();
        document.body.style.overflow = 'hidden';
        clearErrors();
        resetSendCodeButton();
        showForm(`${lastActiveTab}Form`);
    }

    /**
     * 关闭登录表单
     */
    function closeAuthForm() {
        elements.authOverlay.classList.remove('visible');
        document.body.style.overflow = '';
        clearErrors();
        resetSendCodeButton();
    }

    /**
     * 显示指定表单
     * @param {string} formId - 表单ID
     */
    function showForm(formId) {
        elements.authForms.forEach(form => {
            form.classList.remove('active');
            if (form.id === formId) {
                form.classList.add('active');
                form.style.animation = form.id === 'moreSocialForm' ? 'slideUpSocial 0.6s cubic-bezier(0.23, 1, 0.32, 1)' : 'fadeIn 0.3s ease';
            }
        });
        elements.authTabs.forEach(tab => {
            tab.classList.remove('active');
            if (formId.includes(tab.dataset.tab)) {
                tab.classList.add('active');
                lastActiveTab = tab.dataset.tab;
            }
        });
        const authTabs = document.querySelector('.auth-tabs');
        if (formId === 'phoneForm' || formId === 'emailForm' || formId === 'phoneRegisterForm' || formId === 'emailRegisterForm' || formId === 'phonePasswordForm') {
            authTabs.style.display = 'flex';
        } else {
            authTabs.style.display = 'none';
        }
    }

    /**
     * 清除所有错误提示
     */
    function clearErrors() {
        document.querySelectorAll('.error-message').forEach(error => {
            error.classList.remove('visible');
            error.textContent = '';
            error.parentElement.querySelector('input')?.classList.remove('error-shake');
        });
    }

    /**
     * 显示错误提示并添加抖动动画
     * @param {HTMLElement} element - 错误提示元素
     * @param {string} message - 错误信息
     */
    function showError(element, message) {
        element.textContent = message;
        element.classList.add('visible');
        const input = element.parentElement.querySelector('input');
        if (input) {
            input.classList.add('error-shake');
            setTimeout(() => input.classList.remove('error-shake'), 300);
        }
    }

    /**
     * 重置发送验证码按钮
     */
    function resetSendCodeButton() {
        if (countdown) {
            clearInterval(countdown);
        }
        elements.sendCodeButtons.forEach(button => {
            button.disabled = false;
            button.textContent = '发送验证码';
        });
    }

    /**
     * 处理验证码发送逻辑
     * @param {HTMLElement} button - 发送验证码按钮
     */
    function handleSendCode(button) {
        const form = button.closest('.auth-form');
        const phoneInput = form.querySelector('input[type="tel"]') || form.querySelector('input[type="email"]');
        const countryCode = form.querySelector('select')?.value || '';
        const errorElement = phoneInput.parentElement.querySelector('.error-message');

        if (phoneInput.type === 'tel') {
            const phoneRegex = /^\+?\d{10,14}$/;
            if (!phoneRegex.test(countryCode + phoneInput.value.trim())) {
                showError(errorElement, '请输入有效的手机号');
                return;
            }
        } else {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(phoneInput.value.trim())) {
                showError(errorElement, '请输入有效的邮箱');
                return;
            }
        }

        button.disabled = true;
        let timeLeft = 60;
        button.textContent = `${timeLeft}s 后重试`;
        countdown = setInterval(() => {
            timeLeft--;
            button.textContent = `${timeLeft}s 后重试`;
            if (timeLeft <= 0) {
                clearInterval(countdown);
                button.disabled = false;
                button.textContent = '发送验证码';
            }
        }, 1000);

        console.log('发送验证码到:', phoneInput.value);
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间（毫秒）
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 处理社交按钮点击
     * @param {HTMLElement} button - 社交按钮
     */
    const handleSocialButtonClick = debounce(button => {
        const formId = button.dataset.form;
        if (formId) {
            showForm(formId + 'Form');
        } else {
            showError(document.querySelector('.error-message'), '社交登录暂不可用');
        }
    }, 300);

    /**
     * 处理表单提交
     * @param {Event} e - 表单提交事件
     * @param {Object} options - 验证和提交配置
     */
    function handleFormSubmit(e, options) {
        e.preventDefault();
        const { formId, validations, successCallback } = options;
        const form = document.getElementById(formId);
        const submitButton = form.querySelector('.auth-submit');
        let hasError = false;

        validations.forEach(({ inputId, errorId, validate, errorMessage }) => {
            const input = document.getElementById(inputId);
            const errorElement = document.getElementById(errorId);
            if (!validate(input)) {
                showError(errorElement, errorMessage);
                hasError = true;
            }
        });

        if (hasError) return;

        submitButton.disabled = true;
        submitButton.classList.add('loading');
        setTimeout(() => {
            submitButton.disabled = false;
            submitButton.classList.remove('loading');
            successCallback();
            clearErrors();
            resetSendCodeButton();
        }, 1000);
    }

/**
 * 更新头像
 */
function updateAvatar() {
    const avatarImages = document.querySelectorAll('.avatar-icon');
    const avatarSrc = isLoggedIn ? 'images/avatar_active.png' : 'images/avatar_default.png';
    avatarImages.forEach(img => {
        img.src = avatarSrc;
        img.alt = isLoggedIn ? 'Logged In Avatar' : 'Default Avatar';
    });
}

/**
 * 处理位置模块显示
 */
function showLocationOverlay() {
    elements.menuOverlay.classList.remove('visible');
    elements.locationOverlay.classList.add('visible');
    elements.languageOverlay.classList.remove('visible');
    document.body.style.overflow = 'hidden';
}

/**
 * 处理语言模块显示
 */
function showLanguageOverlay() {
    elements.menuOverlay.classList.remove('visible');
    elements.locationOverlay.classList.remove('visible');
    elements.languageOverlay.classList.add('visible');
    document.body.style.overflow = 'hidden';
}

/**
 * 处理手势滑动（侧边栏）
 */
function handleTouchStart(e) {
    touchStartX = e.changedTouches[0].screenX;
}

function handleTouchMove(e) {
    touchEndX = e.changedTouches[0].screenX;
}

function handleTouchEnd() {
    if (touchStartX - touchEndX > 50) {
        // 向左滑，关闭菜单
        closeMenu();
    } else if (touchEndX - touchStartX > 50) {
        // 向右滑，打开菜单
        toggleMenu();
    }
}

/**
 * 初始化事件监听
 */
function initEventListeners() {
    // 头像按钮点击
    elements.avatarToggle.addEventListener('click', toggleMenu);

    // 登录按钮点击
    elements.loginButton.addEventListener('click', openAuthForm);

    // 关闭登录表单
    elements.closeAuth.addEventListener('click', closeAuthForm);
    elements.authBackdrop.addEventListener('click', closeAuthForm);

    // 切换手机号/邮箱选项卡
    elements.authTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            lastActiveTab = tab.dataset.tab;
            showForm(`${lastActiveTab}Form`);
            clearErrors();
        });
    });

    // 表单切换链接
    elements.authLinks.forEach(link => {
        link.addEventListener('click', e => {
            e.preventDefault();
            const formId = link.dataset.form + 'Form';
            showForm(formId);
            clearErrors();
        });
    });

    // 发送验证码按钮
    elements.sendCodeButtons.forEach(button => {
        button.addEventListener('click', () => handleSendCode(button));
    });

    // 社交登录按钮
    elements.socialButtons.forEach(button => {
        button.addEventListener('click', () => handleSocialButtonClick(button));
    });

    // 社交表单返回按钮
    if (elements.socialBackButton) {
        elements.socialBackButton.addEventListener('click', () => {
            showForm(`${lastActiveTab}Form`);
        });
    }

    // 菜单项点击（位置和语言）
    elements.menuItems.forEach(item => {
        item.addEventListener('click', e => {
            e.preventDefault();
            if (item.id === 'locationItem') {
                showLocationOverlay();
            } else if (item.id === 'languageItem') {
                showLanguageOverlay();
            } else if (item.classList.contains('logout')) {
                isLoggedIn = false;
                updateAvatar();
                closeMenu();
            }
        });
    });

    // 位置和语言返回按钮
    elements.locationBack.addEventListener('click', toggleMenu);
    elements.languageBack.addEventListener('click', toggleMenu);

    // 手势滑动支持
    elements.menuOverlay.addEventListener('touchstart', handleTouchStart);
    elements.menuOverlay.addEventListener('touchmove', handleTouchMove);
    elements.menuOverlay.addEventListener('touchend', handleTouchEnd);
    elements.locationOverlay.addEventListener('touchstart', handleTouchStart);
    elements.locationOverlay.addEventListener('touchmove', handleTouchMove);
    elements.locationOverlay.addEventListener('touchend', handleTouchEnd);
    elements.languageOverlay.addEventListener('touchstart', handleTouchStart);
    elements.languageOverlay.addEventListener('touchmove', handleTouchMove);
    elements.languageOverlay.addEventListener('touchend', handleTouchEnd);

    // 表单提交处理
    const formHandlers = [
        {
            formId: 'phoneForm',
            validations: [
                {
                    inputId: 'phoneNumber',
                    errorId: 'phoneError',
                    validate: input => /^\+?\d{10,14}$/.test(document.getElementById('countryCode').value + input.value.trim()),
                    errorMessage: '请输入有效的手机号'
                },
                {
                    inputId: 'verificationCode',
                    errorId: 'codeError',
                    validate: input => input.value.trim().length === 6,
                    errorMessage: '验证码必须为6位'
                },
                {
                    inputId: 'phoneAgreement',
                    errorId: 'phoneError',
                    validate: input => input.checked,
                    errorMessage: '请同意用户协议和隐私协议'
                }
            ],
            successCallback: () => {
                isLoggedIn = true;
                updateAvatar();
                closeAuthForm();
                alert('验证码登录成功！');
            }
        },
        {
            formId: 'phonePasswordForm',
            validations: [
                {
                    inputId: 'passwordPhoneNumber',
                    errorId: 'passwordPhoneError',
                    validate: input => /^\+?\d{10,14}$/.test(document.getElementById('passwordCountryCode').value + input.value.trim()),
                    errorMessage: '请输入有效的手机号'
                },
                {
                    inputId: 'passwordPhonePassword',
                    errorId: 'passwordPhonePasswordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '密码必须至少8位'
                },
                {
                    inputId: 'phonePasswordAgreement',
                    errorId: 'passwordPhoneError',
                    validate: input => input.checked,
                    errorMessage: '请同意用户协议和隐私协议'
                }
            ],
            successCallback: () => {
                isLoggedIn = true;
                updateAvatar();
                closeAuthForm();
                alert('密码登录成功！');
            }
        },
        {
            formId: 'emailForm',
            validations: [
                {
                    inputId: 'emailAddress',
                    errorId: 'emailError',
                    validate: input => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.value.trim()),
                    errorMessage: '请输入有效的邮箱'
                },
                {
                    inputId: 'emailPassword',
                    errorId: 'passwordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '密码必须至少8位'
                },
                {
                    inputId: 'emailAgreement',
                    errorId: 'emailError',
                    validate: input => input.checked,
                    errorMessage: '请同意用户协议和隐私协议'
                }
            ],
            successCallback: () => {
                isLoggedIn = true;
                updateAvatar();
                closeAuthForm();
                alert('邮箱登录成功！');
            }
        },
        {
            formId: 'phoneResetForm',
            validations: [
                {
                    inputId: 'resetPhoneNumber',
                    errorId: 'resetPhoneError',
                    validate: input => /^\+?\d{10,14}$/.test(document.getElementById('resetCountryCode').value + input.value.trim()),
                    errorMessage: '请输入有效的手机号'
                },
                {
                    inputId: 'resetVerificationCode',
                    errorId: 'resetCodeError',
                    validate: input => input.value.trim().length === 6,
                    errorMessage: '验证码必须为6位'
                }
            ],
            successCallback: () => {
                document.getElementById('phoneResetInfo').textContent = `手机号: ${document.getElementById('resetPhoneNumber').value}`;
                showForm('phoneNewPasswordForm');
            }
        },
        {
            formId: 'phoneNewPasswordForm',
            validations: [
                {
                    inputId: 'phoneNewPassword',
                    errorId: 'phoneNewPasswordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '新密码必须至少8位'
                },
                {
                    inputId: 'phoneConfirmNewPassword',
                    errorId: 'phoneConfirmNewPasswordError',
                    validate: input => input.value.trim() === document.getElementById('phoneNewPassword').value.trim(),
                    errorMessage: '两次输入的密码不一致'
                }
            ],
            successCallback: () => {
                showForm('phoneForm');
                alert('密码重置成功，请使用新密码登录！');
            }
        },
        {
            formId: 'emailResetForm',
            validations: [
                {
                    inputId: 'resetEmailAddress',
                    errorId: 'resetEmailError',
                    validate: input => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.value.trim()),
                    errorMessage: '请输入有效的邮箱'
                },
                {
                    inputId: 'resetEmailCode',
                    errorId: 'resetEmailCodeError',
                    validate: input => input.value.trim().length === 6,
                    errorMessage: '验证码必须为6位'
                }
            ],
            successCallback: () => {
                document.getElementById('emailResetInfo').textContent = `邮箱: ${document.getElementById('resetEmailAddress').value}`;
                showForm('emailNewPasswordForm');
            }
        },
        {
            formId: 'emailNewPasswordForm',
            validations: [
                {
                    inputId: 'emailNewPassword',
                    errorId: 'emailNewPasswordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '新密码必须至少8位'
                },
                {
                    inputId: 'emailConfirmNewPassword',
                    errorId: 'emailConfirmNewPasswordError',
                    validate: input => input.value.trim() === document.getElementById('emailNewPassword').value.trim(),
                    errorMessage: '两次输入的密码不一致'
                }
            ],
            successCallback: () => {
                showForm('emailForm');
                alert('密码重置成功，请使用新密码登录！');
            }
        },
        {
            formId: 'phoneRegisterForm',
            validations: [
                {
                    inputId: 'regPhoneNumber',
                    errorId: 'regPhoneError',
                    validate: input => /^\+?\d{10,14}$/.test(document.getElementById('regCountryCode').value + input.value.trim()),
                    errorMessage: '请输入有效的手机号'
                },
                {
                    inputId: 'regVerificationCode',
                    errorId: 'regCodeError',
                    validate: input => input.value.trim().length === 6,
                    errorMessage: '验证码必须为6位'
                },
                {
                    inputId: 'regPhonePassword',
                    errorId: 'regPhonePasswordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '密码必须至少8位'
                },
                {
                    inputId: 'regPhoneAgreement',
                    errorId: 'regPhoneError',
                    validate: input => input.checked,
                    errorMessage: '请同意用户协议和隐私协议'
                }
            ],
            successCallback: () => {
                isLoggedIn = true;
                updateAvatar();
                closeAuthForm();
                alert('注册成功！');
            }
        },
        {
            formId: 'emailRegisterForm',
            validations: [
                {
                    inputId: 'regEmailAddress',
                    errorId: 'regEmailError',
                    validate: input => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.value.trim()),
                    errorMessage: '请输入有效的邮箱'
                },
                {
                    inputId: 'regEmailPassword',
                    errorId: 'regEmailPasswordError',
                    validate: input => input.value.trim().length >= 8,
                    errorMessage: '密码必须至少8位'
                },
                {
                    inputId: 'regEmailConfirmPassword',
                    errorId: 'regEmailConfirmPasswordError',
                    validate: input => input.value.trim() === document.getElementById('regEmailPassword').value.trim(),
                    errorMessage: '两次输入的密码不一致'
                },
                {
                    inputId: 'regEmailAgreement',
                    errorId: 'regEmailError',
                    validate: input => input.checked,
                    errorMessage: '请同意用户协议和隐私协议'
                }
            ],
            successCallback: () => {
                isLoggedIn = true;
                updateAvatar();
                closeAuthForm();
                alert('注册成功！');
            }
        }
    ];

    formHandlers.forEach(handler => {
        const form = document.getElementById(handler.formId);
        if (form) {
            form.addEventListener('submit', e => handleFormSubmit(e, handler));
        }
    });
}

/**
 * 初始化应用
 */
function init() {
    initLogoAnimation();
    initCountryFlags();
    initLocationAndLanguage();
    initEventListeners();
    updateAvatar();
}

// 启动应用
init();
});