/* 全局样式 */
html, body {
    margin: 0;
    padding: 0;
    font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    height: 100vh;
    overflow: hidden;
}

.content { flex: 1; }

/* LOGO 启动动画 */
.logo-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    perspective: 1000px;
}

.logo-overlay img {
    width: 120px;
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1);
    animation: appleBootAnimation 2s cubic-bezier(0.25, 0.1, 0.25, 1) forwards;
}

@keyframes appleBootAnimation {
    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    70% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
    100% { opacity: 1; transform: translate(-50%, calc(-50% - 100px)) scale(0.8); }
}

/* 同步滑动容器 */
.sync-slide-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 180vh;
    padding-bottom: 150px;
    box-sizing: border-box;
}

.logo-section {
    text-align: center;
    margin-bottom: 20px;
}

.logo-section img {
    width: 240px;
    height: auto;
    margin-bottom: 40px;
    transition: transform 0.3s ease;
}

.logo-section img:active {
    transform: scale(0.6);
    filter: brightness(0.9);
}

.search-container {
    display: flex;
    justify-content: center;
}

.search-bar {
    position: relative;
    width: 800px;
    height: 50px;
    border: 1px solid #d9d9d9;
    border-radius: 25px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    background-color: #fff;
    transition: all 0.3s ease;
}

.search-bar:hover { box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2); }
.search-bar:focus-within { box-shadow: 0 8px 12px rgba(0, 0, 0, 0.3); }

.search-bar button {
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 10px;
}

.search-icon {
    width: 24px;
    height: 24px;
    color: rgb(154, 160, 166);
    font-size: 20px;
    transition: color 0.2s ease;
}

.search-icon:hover {
    color: #1a73e8;
}

.search-bar input {
    border: none;
    flex-grow: 1;
    font-size: 16px;
    outline: none;
    padding: 10px;
    background-color: #fff;
    margin-left: -15px;
    max-width: calc(100% - 130px);
}

.search-bar input::placeholder { color: #bbb; }

.search-icons {
    position: absolute;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 头像按钮（替换汉堡菜单） */
.avatar-toggle {
    position: fixed;
    top: 25px;
    right: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 50%;
    border: none;
    box-shadow: none;
    z-index: 1000;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding: 0;
    outline: none;
    position: relative;
    overflow: hidden;
}

.avatar-toggle .avatar-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 1;
    transform: scale(1) rotate(0deg);
}

.avatar-toggle .close-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0) rotate(180deg);
    font-size: 20px;
    color: #333;
    transition: all 0.3s ease;
    opacity: 0;
}

.avatar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}



.ip-display {
    background: rgba(26, 115, 232, 0.1);
    color: #1a73e8;
    padding: 3px 8px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: 600;
    margin-right: 10px;
    letter-spacing: 0.5px;
    border: 1px solid rgba(26, 115, 232, 0.2);
}

.location-display {
    color: #333;
    font-weight: 600;
    font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ip-display {
        font-size: 11px;
        padding: 2px 6px;
        margin-right: 8px;
        letter-spacing: 0.3px;
    }

    .location-display {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .ip-display {
        font-size: 10px;
        padding: 2px 5px;
        margin-right: 6px;
        letter-spacing: 0.2px;
    }

    .location-display {
        font-size: 11px;
    }
}

.avatar-toggle.active {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-toggle.active .avatar-icon {
    opacity: 0;
    transform: scale(0) rotate(-180deg);
}

.avatar-toggle.active .close-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
}

.avatar-toggle.active:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 1);
}

/* 菜单栏 */
.menu-overlay {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(245, 247, 250, 0.95));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: -8px 0 20px rgba(0, 0, 0, 0.08);
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.4s ease;
    z-index: 999;
    opacity: 0;
    pointer-events: none;
    display: flex;
    flex-direction: column;
}

.menu-overlay.visible {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
}

.menu-header {
    padding: 30px 20px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.user-profile {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    color: #555;
    font-size: 20px;
    transition: transform 0.3s ease;
    overflow: hidden;
}

.avatar-icon {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder:hover { transform: scale(1.05); }

.user-info { flex: 1; }

.welcome-text {
    font-weight: 500;
    margin: 0;
    font-size: 15px;
    color: #333;
    letter-spacing: 0.3px;
    line-height: 1.5;
}

.welcome-text a {
    color: #1a73e8;
    text-decoration: none;
    font-weight: 600;
    margin-left: 5px;
    transition: color 0.2s ease;
}

.welcome-text a:hover {
    color: #1557b0;
    text-decoration: underline;
}

.menu-content {
    flex: 1;
    overflow-y: auto;
    padding: 15px 0;
    -webkit-overflow-scrolling: touch;
}

.menu-section { margin-bottom: 20px; }

.menu-section-title {
    font-size: 13px;
    font-weight: 600;
    color: #777;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    margin: 0 20px 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    text-decoration: none;
    color: rgb(15, 15, 15);
    font-size: 14px;
    font-weight: 500;
    height: 20px;
    letter-spacing: 0.2px;
    transition: all 0.2s ease;
    position: relative;
}

.menu-item:hover {
    background: rgba(0, 0, 0, 0.03);
    color: #1a73e8;
    transform: translateX(5px);
}

.menu-item:active {
    background: rgba(0, 0, 0, 0.06);
    transform: scale(0.98);
}

.menu-item.logout { color: #d32f2f; }
.menu-item.logout .menu-icon { color: #d32f2f; }

.menu-icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgb(3, 3, 3);
    font-size: 24px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.arrow-icon {
    position: absolute;
    right: 20px;
    color: #777;
    font-size: 18px;
}

.menu-item:hover .menu-icon,
.menu-item:hover .arrow-icon { color: #1a73e8; }

/* 位置选择模块 */
.location-overlay,
.language-overlay {
    position: fixed;
    top: 0;
    right: 0;
    width: 320px;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(245, 247, 250, 0.95));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: -8px 0 20px rgba(0, 0, 0, 0.08);
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.4s ease;
    z-index: 998;
    opacity: 0;
    pointer-events: none;
    display: flex;
    flex-direction: column;
}

.location-overlay.visible,
.language-overlay.visible {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
}

.location-header,
.language-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.back-button {
    background: none;
    border: none;
    color: #555;
    font-size: 20px;
    cursor: pointer;
    margin-right: 10px;
    transition: color 0.2s ease;
}

.back-button:hover {
    color: #1a73e8;
}

.location-header h2,
.language-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.location-content,
.language-content {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.current-location {
    font-size: 14px;
    color: #555;
    margin-bottom: 20px;
    line-height: 1.5;
}

.current-location span {
    color: #1a73e8;
    font-weight: 500;
}

/* IP位置信息样式 */
.ip-location-info {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.9));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px;
    margin: 10px 0;
    border: 1px solid rgba(26, 115, 232, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.ip-location-info:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.ip-address, .location-text {
    display: flex;
    align-items: center;
    margin: 8px 0;
    font-size: 14px;
}

.ip-address i, .location-text i {
    margin-right: 8px;
    color: #1a73e8;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.ip-text {
    font-family: 'Courier New', monospace;
    background: rgba(26, 115, 232, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    color: #1a73e8;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.location-text span {
    color: #333;
    font-weight: 500;
}

/* 加载动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 位置选择区域样式优化 */
.location-content h3 {
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 8px;
}

/* 重置位置按钮 */
.reset-location-btn {
    width: 100%;
    padding: 10px 16px;
    margin-top: 12px;
    background: linear-gradient(135deg, #1a73e8, #4285f4);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.2);
}

.reset-location-btn:hover {
    background: linear-gradient(135deg, #1557b0, #3367d6);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.reset-location-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(26, 115, 232, 0.2);
}

.reset-location-btn i {
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .ip-location-info {
        padding: 12px;
        margin: 8px 0;
    }

    .ip-address, .location-text {
        font-size: 13px;
    }

    .ip-text {
        padding: 3px 6px;
        font-size: 12px;
    }

    .location-content h3 {
        font-size: 14px;
    }
}

.location-select,
.language-select {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(26, 115, 232, 0.2);
    border-radius: 10px;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    outline: none;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"><path fill="%23777" d="M7 10l5 5 5-5z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.location-select:focus,
.language-select:focus {
    border-color: #1a73e8;
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.15), 0 4px 8px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 1);
}

.location-select:hover,
.language-select:hover {
    border-color: rgba(26, 115, 232, 0.4);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

/* 登录表单弹窗 */
.auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.auth-overlay.visible {
    opacity: 1;
    pointer-events: auto;
}

.auth-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auth-overlay.visible .auth-backdrop { opacity: 1; }

.auth-form-container {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 16px;
    padding: 24px;
    width: 100%;
    max-width: 360px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    transition: transform 0.5s cubic-bezier(0.28, 0.11, 0.32, 1);
    overflow: hidden;
    position: relative;
    z-index: 1;
    animation: slideUp 0.6s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

.auth-overlay.visible .auth-form-container {
    transform: translateY(0);
}

.auth-logo {
    text-align: center;
    margin-bottom: 20px;
}

.auth-logo img {
    width: 140px;
    height: auto;
    transition: transform 0.3s ease;
}

.auth-logo img:hover { transform: scale(1.05); }

.close-button {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0, 0, 0, 0.05);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #555;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-button:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
    transform: rotate(90deg);
}

/* 手机号和邮箱切换按钮 */
.auth-tabs {
    display: flex;
    border-bottom: none;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 10px;
    padding: 4px;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.auth-tab {
    flex: 1;
    padding: 12px;
    background: none;
    border: none;
    font-size: 15px;
    font-weight: 500;
    color: #777;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-sizing: border-box;
}

.auth-tab.active {
    color: #333;
    background: rgba(255, 255, 255, 0.8);
}

.hide-tabs .auth-tabs { display: none; }

/* 表单样式 */
.auth-form {
    padding: 0 16px 20px;
    display: none;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
    -webkit-overflow-scrolling: touch;
    width: 100%;
    box-sizing: border-box;
}

.auth-form.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.auth-form-title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin: 0 0 20px;
    text-align: center;
    letter-spacing: 0.3px;
}

.reset-info {
    font-size: 13px;
    color: #555;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.5;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-group {
    position: relative;
    margin-bottom: 16px;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.form-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #555;
    font-size: 16px;
}

.form-icon.country-flag {
    width: 16px;
    height: 12px;
    object-fit: cover;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    outline: none;
    box-sizing: border-box;
    background: #fff;
}

.form-group select {
    appearance: none;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"><path fill="%23777" d="M7 10l5 5 5-5z"/></svg>') no-repeat right 12px center;
    background-size: 12px;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #1a73e8;
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

.send-code {
    position: absolute;
    right: 8px;
    top: 6px;
    transform: translateY(1px);
    background: #f8fafc;
    color: #333;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-code:disabled {
    background: #f0f0f0;
    color: #999;
    cursor: not-allowed;
}

.send-code:hover:not(:disabled) {
    background: #1a73e8;
    color: #fff;
    transform: translateY(-1px);
}

.send-code:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group.agreement {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #555;
    margin-top: 10px;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.form-group.agreement input {
    width: auto;
    margin-right: 8px;
}

.form-group.agreement .agreement-text {
    color: #333;
    font-weight: 500;
}

.form-group.agreement a {
    color: #1a73e8;
    text-decoration: none;
    transition: color 0.2s ease;
}

.form-group.agreement a:hover {
    color: #1557b0;
    text-decoration: underline;
}

.error-message {
    color: #d32f2f;
    font-size: 11px;
    margin-top: 5px;
    display: none;
    animation: shake 0.3s ease;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.error-message.visible { display: block; }

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.auth-submit {
    width: 100%;
    max-width: 312px;
    padding: 12px;
    background: linear-gradient(90deg, #1a73e8, #2a8eff);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    margin-top: 15px;
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

.auth-submit:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.auth-submit:hover:not(:disabled) {
    background: linear-gradient(90deg, #1557b0, #1a73e8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.auth-submit:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-submit.loading::after {
    content: '\e900';
    font-family: 'remixicon';
    position: absolute;
    right: 12px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    100% { transform: rotate(360deg); }
}

.auth-footer {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.auth-link {
    color: #1a73e8;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #1557b0;
    text-decoration: underline;
}

/* 社交登录区域 */
.social-login {
    margin: 20px 16px;
    text-align: center;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.divider {
    position: relative;
    margin: 20px 0;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
    z-index: 1;
}

.divider span {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    padding: 0 10px;
    font-size: 12px;
    color: #777;
    position: relative;
    z-index: 2;
}

.social-auth-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 312px;
    padding: 12px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 10px;
    box-sizing: border-box;
    margin-left: auto;
    margin-right: auto;
}

.social-auth-button img {
    width: 22px;
    height: 22px;
    margin-right: 8px;
}

.social-auth-button:hover {
    background: #f8fafc;
    transform: translateY(-1px);
}

.social-auth-button:active {
    transform: scale(0.98);
}

.social-auth-button.google {
    background: #ffffff;
    border: 1px solid #e0e0e0;
}

.social-auth-button.more {
    background: #ffffff;
    color: #333;
}

/* 更多社交账号表单 */
.social-form {
    padding: 0 16px 20px;
    max-height: calc(100vh - 180px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    box-sizing: border-box;
}

.social-form-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.social-back-button {
    background: none;
    border: none;
    color: #555;
    font-size: 20px;
    cursor: pointer;
    margin-right: 1px;
    transition: color 0.2s ease;
}

.social-back-button:hover {
    color: #1a73e8;
}

.social-options {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 100%;
    max-width: 312px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
}

.social-form .auth-form-title {
    margin-bottom: 0;
    text-align: center;
    font-size: 16px;
}

.social-form .auth-footer {
    justify-content: center;
    margin-top: 20px;
}

.auth-form.social-form.active .auth-logo,
.auth-form.social-form.active .close-button {
    display: none;
}

@keyframes slideUpSocial {
    0% { transform: translateY(50%); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

/* 自定义滚动条 */
.auth-form::-webkit-scrollbar,
.social-form::-webkit-scrollbar,
.menu-content::-webkit-scrollbar,
.location-content::-webkit-scrollbar,
.language-content::-webkit-scrollbar {
    width: 8px;
}

.auth-form::-webkit-scrollbar-track,
.social-form::-webkit-scrollbar-track,
.menu-content::-webkit-scrollbar-track,
.location-content::-webkit-scrollbar-track,
.language-content::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
}

.auth-form::-webkit-scrollbar-thumb,
.social-form::-webkit-scrollbar-thumb,
.menu-content::-webkit-scrollbar-thumb,
.location-content::-webkit-scrollbar-thumb,
.language-content::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.auth-form::-webkit-scrollbar-thumb:hover,
.social-form::-webkit-scrollbar-thumb:hover,
.menu-content::-webkit-scrollbar-thumb:hover,
.location-content::-webkit-scrollbar-thumb:hover,
.language-content::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .auth  .auth-overlay {
        align-items: flex-start;
    }

    .auth-form-container {
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        border-radius: 0;
        padding: 24px;
        margin: 0;
        box-shadow: none;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }

    .auth-tabs,
    .form-group,
    .form-group.agreement,
    .error-message,
    .auth-submit,
    .auth-footer,
    .social-login,
    .social-auth-button,
    .social-options {
        max-width: 100%;
    }

    .form-group.agreement {
        font-size: 12px;
    }
    
    .form-group.agreement input {
        margin-right: 8px;
    }

    .logo-section img {
        width: 200px;
        margin-bottom: 40px;
    }

    .logo-overlay img {
        width: 100px;
    }

    @keyframes appleBootAnimation {
        100% {
            transform: translate(-50%, calc(-50% - 80px)) scale(0.7);
        }
    }

    .search-bar { width: 450px; }
    .menu-overlay,
    .location-overlay,
    .language-overlay { width: 100%; }
    .social-auth-button img { width: 22px; height: 22px; }
}

@media (max-width: 480px) {
    .auth-form-container {
        padding: 20px;
    }

    .form-group.agreement {
        font-size: 12px;
    }

    .logo-section img {
        width: 160px;
        margin-bottom: 40px;
    }

    .logo-overlay img {
        width: 70%;
        max-width: 120px;
    }

    .search-bar { 
        width: 360px; 
        padding: 0 10px;
    }
}

@media (max-width: 400px) {
    .auth-form-container {
        padding: 16px;
    }

    .form-group.agreement {
        font-size: 11px;
    }

    .search-bar { 
        width: 310px; 
        padding: 0 8px;
    }

    .logo-section img { 
        width: 150px; 
    }
}