<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border-left: 4px solid #1a73e8;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #1557b0;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            border-left-color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>位置功能测试</h1>
        <p>这个页面用于测试IP位置获取功能是否正常工作。</p>
        
        <button onclick="testIPLocation()">测试IP位置获取</button>
        <button onclick="testAllAPIs()">测试所有API</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testIPLocation() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result loading">正在获取IP位置信息...</div>';
            
            try {
                // 使用与主应用相同的API列表
                const apis = [
                    {
                        name: 'ipapi.co',
                        url: 'https://ipapi.co/json/',
                        parser: (data) => ({
                            ip: data.ip,
                            country: data.country_name,
                            region: data.region,
                            city: data.city
                        })
                    },
                    {
                        name: 'ip-api.com',
                        url: 'https://ip-api.com/json/',
                        parser: (data) => ({
                            ip: data.query,
                            country: data.country,
                            region: data.regionName,
                            city: data.city
                        })
                    },
                    {
                        name: 'ipinfo.io',
                        url: 'https://ipinfo.io/json',
                        parser: (data) => ({
                            ip: data.ip,
                            country: data.country,
                            region: data.region,
                            city: data.city
                        })
                    }
                ];

                let locationData = null;
                let usedAPI = '';
                
                // 依次尝试各个API
                for (const api of apis) {
                    try {
                        const response = await fetch(api.url);
                        if (response.ok) {
                            const data = await response.json();
                            locationData = api.parser(data);
                            usedAPI = api.name;
                            break;
                        }
                    } catch (apiError) {
                        console.warn(`API ${api.url} 失败:`, apiError);
                        continue;
                    }
                }

                if (locationData) {
                    const formattedLocation = formatLocationDisplay(locationData);
                    resultsDiv.innerHTML = `
                        <div class="result">
                            <h3>✅ 位置获取成功</h3>
                            <p><strong>使用的API:</strong> ${usedAPI}</p>
                            <p><strong>原始数据:</strong></p>
                            <pre>${JSON.stringify(locationData, null, 2)}</pre>
                            <p><strong>格式化显示:</strong></p>
                            ${formattedLocation}
                        </div>
                    `;
                } else {
                    throw new Error('所有IP位置API都无法访问');
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 位置获取失败</h3>
                        <p><strong>错误信息:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testAllAPIs() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result loading">正在测试所有API...</div>';
            
            const apis = [
                { name: 'ipapi.co', url: 'https://ipapi.co/json/' },
                { name: 'ip-api.com', url: 'https://ip-api.com/json/' },
                { name: 'ipinfo.io', url: 'https://ipinfo.io/json' }
            ];

            let results = '<h3>API测试结果:</h3>';
            
            for (const api of apis) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(api.url);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    
                    if (response.ok) {
                        const data = await response.json();
                        results += `
                            <div class="result">
                                <strong>✅ ${api.name}</strong> - 响应时间: ${responseTime}ms<br>
                                <small>状态: ${response.status} ${response.statusText}</small>
                            </div>
                        `;
                    } else {
                        results += `
                            <div class="result error">
                                <strong>❌ ${api.name}</strong> - HTTP错误<br>
                                <small>状态: ${response.status} ${response.statusText}</small>
                            </div>
                        `;
                    }
                } catch (error) {
                    results += `
                        <div class="result error">
                            <strong>❌ ${api.name}</strong> - 网络错误<br>
                            <small>错误: ${error.message}</small>
                        </div>
                    `;
                }
            }
            
            resultsDiv.innerHTML = results;
        }

        function formatLocationDisplay(locationData) {
            const { ip, country, region, city } = locationData;

            // 中文国家名称映射（扩展版）
            const countryMap = {
                'China': '中国',
                'United States': '美国',
                'Japan': '日本',
                'South Korea': '韩国',
                'United Kingdom': '英国',
                'Germany': '德国',
                'France': '法国',
                'Canada': '加拿大',
                'Australia': '澳大利亚',
                'Singapore': '新加坡',
                'Hong Kong': '香港',
                'Taiwan': '台湾',
                'Macao': '澳门',
                'Russia': '俄罗斯',
                'India': '印度',
                'Brazil': '巴西',
                'Mexico': '墨西哥',
                'Thailand': '泰国',
                'Vietnam': '越南',
                'Malaysia': '马来西亚',
                'Indonesia': '印度尼西亚',
                'Philippines': '菲律宾',
                'Netherlands': '荷兰',
                'Italy': '意大利',
                'Spain': '西班牙',
                'Switzerland': '瑞士',
                'Sweden': '瑞典',
                'Norway': '挪威',
                'Denmark': '丹麦',
                'Finland': '芬兰'
            };

            const countryName = countryMap[country] || country;

            // 构建位置字符串，确保格式为"国家省份-城市"
            let locationParts = [countryName];

            // 添加省份/州/地区（如果存在且不同于国家名）
            if (region && region !== country && region !== countryName) {
                locationParts.push(region);
            }

            // 添加城市（如果存在且不同于地区名）
            if (city && city !== region && city !== country && city !== countryName) {
                locationParts.push(city);
            }

            const locationString = locationParts.join('-');

            return `
                <div style="background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 12px; padding: 16px; border: 1px solid rgba(26, 115, 232, 0.1);">
                    <div style="display: flex; align-items: center; margin: 8px 0;">
                        <span style="margin-right: 8px;">🌐</span>
                        <span style="font-family: monospace; background: rgba(26, 115, 232, 0.1); padding: 4px 8px; border-radius: 6px; color: #1a73e8; font-weight: 600;">${ip}</span>
                    </div>
                    <div style="display: flex; align-items: center; margin: 8px 0;">
                        <span style="margin-right: 8px;">📍</span>
                        <span style="color: #333; font-weight: 500;">${locationString}</span>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
