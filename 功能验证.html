<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位置功能验证 - HaoSurge</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-title {
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 6px;
            background: white;
        }
        .success {
            border-left: 4px solid #4caf50;
            background: #f1f8e9;
        }
        .error {
            border-left: 4px solid #f44336;
            background: #ffebee;
        }
        .loading {
            border-left: 4px solid #ff9800;
            background: #fff3e0;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1557b0;
        }
        .ip-display {
            background: rgba(26, 115, 232, 0.1);
            color: #1a73e8;
            padding: 3px 8px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            margin-right: 10px;
            letter-spacing: 0.5px;
            border: 1px solid rgba(26, 115, 232, 0.2);
        }
        .location-display {
            color: #333;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 位置功能验证测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. IP位置获取测试</div>
            <button onclick="testIPLocation()">开始测试</button>
            <div id="ipLocationResult" class="result loading">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 位置格式化测试</div>
            <button onclick="testLocationFormat()">测试格式化</button>
            <div id="formatResult" class="result loading">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 中文国家名称映射测试</div>
            <button onclick="testCountryMapping()">测试映射</button>
            <div id="mappingResult" class="result loading">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 显示样式测试</div>
            <div class="result success">
                <strong>样式预览：</strong><br>
                <span class="ip-display">*************</span>
                <span class="location-display">中国福建-厦门</span>
            </div>
        </div>
    </div>

    <script>
        // 测试IP位置获取
        async function testIPLocation() {
            const resultDiv = document.getElementById('ipLocationResult');
            resultDiv.className = 'result loading';
            resultDiv.innerHTML = '正在获取IP位置信息...';

            try {
                const apis = [
                    {
                        url: 'https://ipapi.co/json/',
                        name: 'ipapi.co',
                        parser: (data) => ({
                            ip: data.ip,
                            country: data.country_name,
                            region: data.region,
                            city: data.city
                        })
                    },
                    {
                        url: 'https://ip-api.com/json/',
                        name: 'ip-api.com',
                        parser: (data) => ({
                            ip: data.query,
                            country: data.country,
                            region: data.regionName,
                            city: data.city
                        })
                    }
                ];

                let success = false;
                for (const api of apis) {
                    try {
                        const response = await fetch(api.url);
                        if (response.ok) {
                            const data = await response.json();
                            const locationData = api.parser(data);
                            
                            resultDiv.className = 'result success';
                            resultDiv.innerHTML = `
                                <strong>✅ 获取成功！</strong><br>
                                API: ${api.name}<br>
                                IP: ${locationData.ip}<br>
                                国家: ${locationData.country}<br>
                                地区: ${locationData.region}<br>
                                城市: ${locationData.city}
                            `;
                            success = true;
                            break;
                        }
                    } catch (error) {
                        console.warn(`API ${api.name} 失败:`, error);
                    }
                }

                if (!success) {
                    throw new Error('所有API都无法访问');
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 获取失败: ${error.message}`;
            }
        }

        // 测试位置格式化
        function testLocationFormat() {
            const resultDiv = document.getElementById('formatResult');
            
            const testCases = [
                { ip: '*************', country: 'China', region: 'Fujian', city: 'Xiamen' },
                { ip: '********', country: 'United States', region: 'California', city: 'Los Angeles' },
                { ip: '**********', country: 'Japan', region: 'Tokyo', city: 'Tokyo' },
                { ip: '***********', country: 'Singapore', region: 'Singapore', city: 'Singapore' }
            ];

            let results = '<strong>✅ 格式化测试结果：</strong><br><br>';
            
            testCases.forEach((testCase, index) => {
                const formatted = formatLocationForTest(testCase);
                results += `${index + 1}. ${formatted}<br>`;
            });

            resultDiv.className = 'result success';
            resultDiv.innerHTML = results;
        }

        // 测试国家名称映射
        function testCountryMapping() {
            const resultDiv = document.getElementById('mappingResult');
            
            const countryMap = {
                'China': '中国',
                'United States': '美国',
                'Japan': '日本',
                'South Korea': '韩国',
                'United Kingdom': '英国',
                'Germany': '德国',
                'France': '法国',
                'Singapore': '新加坡',
                'Russia': '俄罗斯',
                'India': '印度'
            };

            let results = '<strong>✅ 国家名称映射测试：</strong><br><br>';
            
            Object.entries(countryMap).forEach(([english, chinese]) => {
                results += `${english} → ${chinese}<br>`;
            });

            resultDiv.className = 'result success';
            resultDiv.innerHTML = results;
        }

        // 位置格式化函数（测试用）
        function formatLocationForTest(locationData) {
            const { ip, country, region, city } = locationData;
            
            const countryMap = {
                'China': '中国',
                'United States': '美国',
                'Japan': '日本',
                'Singapore': '新加坡'
            };

            const countryName = countryMap[country] || country;
            let locationParts = [countryName];

            if (region && region !== country && region !== countryName) {
                locationParts.push(region);
            }

            if (city && city !== region && city !== country && city !== countryName) {
                locationParts.push(city);
            }

            const locationString = locationParts.join('-');
            
            return `<span class="ip-display">${ip}</span><span class="location-display">${locationString}</span>`;
        }
    </script>
</body>
</html>
