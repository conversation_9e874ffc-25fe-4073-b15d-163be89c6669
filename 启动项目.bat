@echo off
chcp 65001 >nul
echo ========================================
echo    HaoSurge Browser Project Launcher
echo ========================================
echo.

REM 检查端口8080是否被占用
echo Checking port 8080...
netstat -an | find "8080" >nul
if %errorlevel% equ 0 (
    echo Port 8080 is already in use. Stopping existing processes...
    for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
        echo Stopping process %%a...
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

echo Starting local server on port 8080...
echo.

REM 优先使用Python HTTP服务器
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Python HTTP server...
    echo Server URL: http://localhost:8080
    echo.
    echo Available pages:
    echo - Main page: http://localhost:8080
    echo - Location test: http://localhost:8080/test-location.html
    echo - Function test: http://localhost:8080/功能验证.html
    echo.
    echo Press Ctrl+C to stop the server
    echo ========================================
    python -m http.server 8080
) else (
    REM 如果Python不可用，尝试Node.js
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo Using Node.js HTTP server...
        echo Server URL: http://localhost:8080
        echo.
        echo Available pages:
        echo - Main page: http://localhost:8080
        echo - Location test: http://localhost:8080/test-location.html
        echo - Function test: http://localhost:8080/功能验证.html
        echo.
        echo Press Ctrl+C to stop the server
        echo ========================================
        npx http-server -p 8080 -c-1
    ) else (
        echo Error: Neither Python nor Node.js is available
        echo.
        echo Please install Python or Node.js to run the server
        echo Or open index.html directly in your browser
        echo.
        pause
        exit /b 1
    )
)
