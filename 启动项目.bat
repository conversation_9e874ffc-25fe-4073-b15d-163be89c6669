@echo off
chcp 65001 >nul
echo ========================================
echo    HaoSurge Browser Project Launcher
echo ========================================
echo.

echo Starting local server...
echo.

REM Try to start Python HTTP server
python -m http.server 8080 2>nul
if %errorlevel% neq 0 (
    echo Python server failed, trying Node.js...
    npx http-server -p 8080 -c-1 2>nul
    if %errorlevel% neq 0 (
        echo Cannot start server, please ensure Python or Node.js is installed
        echo.
        echo You can also open index.html directly in browser
        pause
        exit /b 1
    )
)

echo Server started successfully!
echo.
echo Please visit the following address in your browser:
echo http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
pause
