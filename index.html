<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>HaoSurge - 发现未知的精彩世界</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.loli.net/css2?family=Alibaba PuHuiTi 2.0:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css">
</head>

<body>
    <!-- LOGO启动动画 -->
    <div class="logo-overlay" id="logoOverlay">
        <img src="images/hao_surge_logo.png" alt="HaoSurge Logo">
    </div>

    <!-- 头像按钮（替换汉堡菜单） -->
    <button class="avatar-toggle" id="avatarToggle">
        <img src="images/avatar_default.png" alt="Avatar" class="avatar-icon">
    </button>

    <!-- 侧边栏菜单 -->
    <div class="menu-overlay" id="menuOverlay">
        <div class="menu-header">
            <div class="user-profile">
                <div class="avatar-placeholder">
                    <img src="images/avatar_default.png" alt="Default Avatar" class="avatar-icon">
                </div>
                <div class="user-info">
                    <p class="welcome-text">点击 <a href="#" class="login-button" id="loginButton">登录</a> 获取完整体验</p>
                </div>
            </div>
        </div>
        <div class="menu-content">
            <div class="menu-section">
                <h3 class="menu-section-title">电子钱包</h3>
                <a href="#" class="menu-item"><i class="ri-coin-line menu-icon"></i><span>奖励</span></a>
                <a href="#" class="menu-item"><i class="ri-wallet-line menu-icon"></i><span>收入</span></a>
                <a href="#" class="menu-item"><i class="ri-bank-card-line menu-icon"></i><span>提现</span></a>
                <a href="#" class="menu-item"><i class="ri-money-dollar-circle-line menu-icon"></i><span>付款</span></a>
                <a href="#" class="menu-item"><i class="ri-mail-line menu-icon"></i><span>邮箱</span></a>
            </div>
            <div class="menu-section">
                <h3 class="menu-section-title">应用</h3>
                <a href="#" class="menu-item" id="locationItem"><i
                        class="ri-map-pin-line menu-icon"></i><span>位置</span><i
                        class="ri-arrow-right-s-line arrow-icon"></i></a>
                <a href="#" class="menu-item" id="languageItem"><i
                        class="ri-global-line menu-icon"></i><span>语言</span><i
                        class="ri-arrow-right-s-line arrow-icon"></i></a>
                <a href="#" class="menu-item"><i class="ri-brush-line menu-icon"></i><span>外观</span></a>
            </div>
            <div class="menu-section">
                <h3 class="menu-section-title">合作伙伴</h3>
                <a href="#" class="menu-item"><i class="ri-advertisement-line menu-icon"></i><span>广告</span></a>
                <a href="#" class="menu-item"><i class="ri-briefcase-line menu-icon"></i><span>商务合作</span></a>
                <a href="#" class="menu-item"><i class="ri-contacts-line menu-icon"></i><span>联系我们</span></a>
            </div>
            <div class="menu-section">
                <h3 class="menu-section-title">关于</h3>
                <a href="#" class="menu-item"><i class="ri-information-line menu-icon"></i><span>关于我们</span></a>
                <a href="#" class="menu-item"><i class="ri-file-text-line menu-icon"></i><span>用户协议</span></a>
                <a href="#" class="menu-item"><i class="ri-lock-line menu-icon"></i><span>隐私政策</span></a>
                <a href="#" class="menu-item"><i class="ri-book-open-line menu-icon"></i><span>使用条款</span></a>
                <a href="#" class="menu-item"><i class="ri-refresh-line menu-icon"></i><span>检查更新</span></a>
            </div>
            <div class="menu-section">
                <a href="#" class="menu-item logout"><i class="ri-logout-box-line menu-icon"></i><span>退出登录</span></a>
            </div>
        </div>
    </div>

    <!-- 位置选择模块 -->
    <div class="location-overlay" id="locationOverlay">
        <div class="location-header">
            <button class="back-button" id="locationBack"><i class="ri-arrow-left-line"></i></button>
            <h2>选择位置</h2>
        </div>
        <div class="location-content">
            <div class="current-location">
                <h3 style="margin: 0 0 12px 0; color: #333; font-size: 16px; font-weight: 600;">
                    <i class="ri-radar-line" style="margin-right: 8px; color: #1a73e8;"></i>
                    当前真实位置
                </h3>
                <div id="currentLocation">
                    <div style="text-align: center; color: #999; padding: 20px;">
                        <i class="ri-loader-4-line" style="font-size: 24px; animation: spin 1s linear infinite;"></i>
                        <p style="margin: 8px 0 0 0;">正在获取位置信息...</p>
                    </div>
                </div>
                <button id="resetLocationBtn" class="reset-location-btn" style="display: none;">
                    <i class="ri-refresh-line"></i>
                    恢复真实位置
                </button>
            </div>

            <div style="margin-top: 24px;">
                <h3 style="margin: 0 0 12px 0; color: #333; font-size: 16px; font-weight: 600;">
                    <i class="ri-map-pin-line" style="margin-right: 8px; color: #1a73e8;"></i>
                    选择其他位置
                </h3>
                <select id="locationSelect" class="location-select">
                    <option value="" disabled selected>选择其他位置...</option>
                    <option value="CN-BJ">中国北京-北京</option>
                    <option value="CN-SH">中国上海-上海</option>
                    <option value="CN-GD">中国广东-广州</option>
                    <option value="CN-GD-SZ">中国广东-深圳</option>
                    <option value="CN-FJ-XM">中国福建-厦门</option>
                    <option value="CN-HK">中国-香港</option>
                    <option value="CN-TW">中国-台湾</option>
                    <option value="US-NY">美国纽约-纽约</option>
                    <option value="US-CA">美国加利福尼亚-洛杉矶</option>
                    <option value="GB-LON">英国伦敦-伦敦</option>
                    <option value="JP-TOK">日本东京-东京</option>
                    <option value="KR-SEO">韩国首尔-首尔</option>
                    <option value="SG-SG">新加坡-新加坡</option>
                </select>
            </div>
        </div>
    </div>

    <!-- 语言选择模块 -->
    <div class="language-overlay" id="languageOverlay">
        <div class="language-header">
            <button class="back-button" id="languageBack"><i class="ri-arrow-left-line"></i></button>
            <h2>选择语言</h2>
        </div>
        <div class="language-content">
            <select id="languageSelect" class="language-select">
                <option value="zh-CN">中文 (简体)</option>
                <option value="en-US">English</option>
                <option value="ja-JP">日本語</option>
                <option value="hi-IN">हिन्दी</option>
                <option value="es-ES">Español</option>
            </select>
        </div>
    </div>

    <!-- 登录表单弹窗 -->
    <div class="auth-overlay" id="authOverlay">
        <div class="auth-backdrop" id="authBackdrop"></div>
        <div class="auth-form-container">
            <div class="auth-logo">
                <img src="images/surge_logo.png" alt="Surge Logo">
            </div>
            <button class="close-button" id="closeAuth">
                <i class="ri-close-line"></i>
            </button>

            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="phone">手机号</button>
                <button class="auth-tab" data-tab="email">邮箱</button>
            </div>

            <!-- 手机号验证码登录表单 -->
            <form id="phoneForm" class="auth-form active">
                <div class="form-group">
                    <img class="form-icon country-flag" src="https://flagicons.lipis.dev/flags/4x3/cn.svg"
                        alt="Country Flag">
                    <select id="countryCode" class="country-code" required>
                        <option value="+86" data-flag="https://flagicons.lipis.dev/flags/4x3/cn.svg" selected>+86 (中国)
                        </option>
                        <option value="+1" data-flag="https://flagicons.lipis.dev/flags/4x3/us.svg">+1 (美国)</option>
                        <option value="+44" data-flag="https://flagicons.lipis.dev/flags/4x3/gb.svg">+44 (英国)</option>
                        <option value="+91" data-flag="https://flagicons.lipis.dev/flags/4x3/in.svg">+91 (印度)</option>
                        <option value="+81" data-flag="https://flagicons.lipis.dev/flags/4x3/jp.svg">+81 (日本)</option>
                    </select>
                </div>
                <div class="form-group">
                    <i class="ri-smartphone-line form-icon"></i>
                    <input type="tel" id="phoneNumber" placeholder="请输入手机号" required>
                    <div class="error-message" id="phoneError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-shield-keyhole-line form-icon"></i>
                    <input type="text" id="verificationCode" placeholder="请输入验证码" required>
                    <button type="button" class="send-code" id="sendCode">发送验证码</button>
                    <div class="error-message" id="codeError"></div>
                </div>
                <div class="form-group agreement">
                    <input type="checkbox" id="phoneAgreement" required>
                    <label for="phoneAgreement">已阅读并同意<span class="agreement-text">《用户协议》</span>和<span
                            class="agreement-text">《隐私协议》</span></label>
                </div>
                <button type="submit" class="auth-submit" id="phoneSubmit">验证码登录</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="phonePassword">密码登录</a>
                    <a href="#" class="auth-link" data-form="phoneRegister">立即注册</a>
                </div>
                <div class="social-login">
                    <div class="divider">
                        <span>或</span>
                    </div>
                    <button type="button" class="social-auth-button google">
                        <img src="images/social/google.png" alt="Google Icon">
                        使用Google账号登录
                    </button>
                    <button type="button" class="social-auth-button more" data-form="moreSocial">
                        或者使用更多账号登录
                    </button>
                </div>
            </form>

            <!-- 手机号密码登录表单 -->
            <form id="phonePasswordForm" class="auth-form">
                <div class="form-group">
                    <img class="form-icon country-flag" src="https://flagicons.lipis.dev/flags/4x3/cn.svg"
                        alt="Country Flag">
                    <select id="passwordCountryCode" class="country-code" required>
                        <option value="+86" data-flag="https://flagicons.lipis.dev/flags/4x3/cn.svg" selected>+86 (中国)
                        </option>
                        <option value="+1" data-flag="https://flagicons.lipis.dev/flags/4x3/us.svg">+1 (美国)</option>
                        <option value="+44" data-flag="https://flagicons.lipis.dev/flags/4x3/gb.svg">+44 (英国)</option>
                        <option value="+91" data-flag="https://flagicons.lipis.dev/flags/4x3/in.svg">+91 (印度)</option>
                        <option value="+81" data-flag="https://flagicons.lipis.dev/flags/4x3/jp.svg">+81 (日本)</option>
                    </select>
                </div>
                <div class="form-group">
                    <i class="ri-smartphone-line form-icon"></i>
                    <input type="tel" id="passwordPhoneNumber" placeholder="请输入手机号" required>
                    <div class="error-message" id="passwordPhoneError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="passwordPhonePassword" placeholder="请输入密码" required>
                    <div class="error-message" id="passwordPhonePasswordError"></div>
                </div>
                <div class="form-group agreement">
                    <input type="checkbox" id="phonePasswordAgreement" required>
                    <label for="phonePasswordAgreement">已阅读并同意<span class="agreement-text">《用户协议》</span>和<span
                            class="agreement-text">《隐私协议》</span></label>
                </div>
                <button type="submit" class="auth-submit" id="phonePasswordSubmit">密码登录</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="phoneReset">忘记密码？</a>
                    <a href="#" class="auth-link" data-form="phone">返回验证码登录</a>
                </div>
                <div class="social-login">
                    <div class="divider">
                        <span>或</span>
                    </div>
                    <button type="button" class="social-auth-button google">
                        <img src="images/social/google.png" alt="Google Icon">
                        使用Google账号登录
                    </button>
                    <button type="button" class="social-auth-button more" data-form="moreSocial">
                        或者使用更多账号登录
                    </button>
                </div>
            </form>

            <!-- 邮箱登录表单 -->
            <form id="emailForm" class="auth-form">
                <div class="form-group">
                    <i class="ri-mail-line form-icon"></i>
                    <input type="email" id="emailAddress" placeholder="请输入邮箱" required>
                    <div class="error-message" id="emailError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="emailPassword" placeholder="请输入密码" required>
                    <div class="error-message" id="passwordError"></div>
                </div>
                <div class="form-group agreement">
                    <input type="checkbox" id="emailAgreement" required>
                    <label for="emailAgreement">已阅读并同意<span class="agreement-text">《用户协议》</span>和<span
                            class="agreement-text">《隐私协议》</span></label>
                </div>
                <button type="submit" class="auth-submit" id="emailSubmit">登录</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="emailReset">忘记密码？</a>
                    <a href="#" class="auth-link" data-form="emailRegister">立即注册</a>
                </div>
                <div class="social-login">
                    <div class="divider">
                        <span>或</span>
                    </div>
                    <button type="button" class="social-auth-button google">
                        <img src="images/social/google.png" alt="Google Icon">
                        使用Google账号登录
                    </button>
                    <button type="button" class="social-auth-button more" data-form="moreSocial">
                        或者使用更多账号登录
                    </button>
                </div>
            </form>

            <!-- 手机号重置密码表单 -->
            <form id="phoneResetForm" class="auth-form hide-tabs">
                <h2 class="auth-form-title">重置手机号登录密码</h2>
                <div class="form-group">
                    <img class="form-icon country-flag" src="https://flagicons.lipis.dev/flags/4x3/cn.svg"
                        alt="Country Flag">
                    <select id="resetCountryCode" class="country-code" required>
                        <option value="+86" data-flag="https://flagicons.lipis.dev/flags/4x3/cn.svg" selected>+86 (中国)
                        </option>
                        <option value="+1" data-flag="https://flagicons.lipis.dev/flags/4x3/us.svg">+1 (美国)</option>
                        <option value="+44" data-flag="https://flagicons.lipis.dev/flags/4x3/gb.svg">+44 (英国)</option>
                        <option value="+91" data-flag="https://flagicons.lipis.dev/flags/4x3/in.svg">+91 (印度)</option>
                        <option value="+81" data-flag="https://flagicons.lipis.dev/flags/4x3/jp.svg">+81 (日本)</option>
                    </select>
                </div>
                <div class="form-group">
                    <i class="ri-smartphone-line form-icon"></i>
                    <input type="tel" id="resetPhoneNumber" placeholder="请输入手机号" required>
                    <div class="error-message" id="resetPhoneError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-shield-keyhole-line form-icon"></i>
                    <input type="text" id="resetVerificationCode" placeholder="请输入验证码" required>
                    <button type="button" class="send-code" id="resetSendCode">发送验证码</button>
                    <div class="error-message" id="resetCodeError"></div>
                </div>
                <button type="submit" class="auth-submit" id="resetPhoneSubmit">下一步</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="phone">返回登录</a>
                </div>
            </form>

            <!-- 手机号新密码表单 -->
            <form id="phoneNewPasswordForm" class="auth-form hide-tabs">
                <h2 class="auth-form-title">重置手机号登录密码</h2>
                <p class="reset-info" id="phoneResetInfo"></p>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="phoneNewPassword" placeholder="请输入新密码" required>
                    <div class="error-message" id="phoneNewPasswordError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="phoneConfirmNewPassword" placeholder="请确认新密码" required>
                    <div class="error-message" id="phoneConfirmNewPasswordError"></div>
                </div>
                <button type="submit" class="auth-submit" id="phoneNewPasswordSubmit">重置密码</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="phone">返回登录</a>
                </div>
            </form>

            <!-- 邮箱重置密码表单 -->
            <form id="emailResetForm" class="auth-form hide-tabs">
                <h2 class="auth-form-title">重置邮箱登录密码</h2>
                <div class="form-group">
                    <i class="ri-mail-line form-icon"></i>
                    <input type="email" id="resetEmailAddress" placeholder="请输入邮箱" required>
                    <div class="error-message" id="resetEmailError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-shield-keyhole-line form-icon"></i>
                    <input type="text" id="resetEmailCode" placeholder="请输入验证码" required>
                    <button type="button" class="send-code" id="resetEmailSendCode">发送验证码</button>
                    <div class="error-message" id="resetEmailCodeError"></div>
                </div>
                <button type="submit" class="auth-submit" id="resetEmailSubmit">下一步</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="email">返回登录</a>
                </div>
            </form>

            <!-- 邮箱新密码表单 -->
            <form id="emailNewPasswordForm" class="auth-form hide-tabs">
                <h2 class="auth-form-title">重置邮箱登录ById登录密码</h2>
                <p class="reset-info" id="emailResetInfo"></p>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="emailNewPassword" placeholder="请输入新密码" required>
                    <div class="error-message" id="emailNewPasswordError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="emailConfirmNewPassword" placeholder="请确认新密码" required>
                    <div class="error-message" id="emailConfirmNewPasswordError"></div>
                </div>
                <button type="submit" class="auth-submit" id="emailNewPasswordSubmit">重置密码</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="email">返回登录</a>
                </div>
            </form>

            <!-- 手机号注册表单 -->
            <form id="phoneRegisterForm" class="auth-form">
                <div class="form-group">
                    <img class="form-icon country-flag" src="https://flagicons.lipis.dev/flags/4x3/cn.svg"
                        alt="Country Flag">
                    <select id="regCountryCode" class="country-code" required>
                        <option value="+86" data-flag="https://flagicons.lipis.dev/flags/4x3/cn.svg" selected>+86 (中国)
                        </option>
                        <option value="+1" data-flag="https://flagicons.lipis.dev/flags/4x3/us.svg">+1 (美国)</option>
                        <option value="+44" data-flag="https://flagicons.lipis.dev/flags/4x3/gb.svg">+44 (英国)</option>
                        <option value="+91" data-flag="https://flagicons.lipis.dev/flags/4x3/in.svg">+91 (印度)</option>
                        <option value="+81" data-flag="https://flagicons.lipis.dev/flags/4x3/jp.svg">+81 (日本)</option>
                    </select>
                </div>
                <div class="form-group">
                    <i class="ri-smartphone-line form-icon"></i>
                    <input type="tel" id="regPhoneNumber" placeholder="请输入手机号" required>
                    <div class="error-message" id="regPhoneError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-shield-keyhole-line form-icon"></i>
                    <input type="text" id="regVerificationCode" placeholder="请输入验证码" required>
                    <button type="button" class="send-code" id="regSendCode">发送验证码</button>
                    <div class="error-message" id="regCodeError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="regPhonePassword" placeholder="请输入密码" required>
                    <div class="error-message" id="regPhonePasswordError"></div>
                </div>
                <div class="form-group agreement">
                    <input type="checkbox" id="regPhoneAgreement" required>
                    <label for="regPhoneAgreement">已阅读并同意<span class="agreement-text">《用户协议》</span>和<span
                            class="agreement-text">《隐私协议》</span></label>
                </div>
                <button type="submit" class="auth-submit" id="regPhoneSubmit">注册</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="phone">返回登录</a>
                </div>
                <div class="social-login">
                    <div class="divider">
                        <span>或</span>
                    </div>
                    <button type="button" class="social-auth-button google">
                        <img src="images/social/google.png" alt="Google Icon">
                        使用Google账号登录
                    </button>
                    <button type="button" class="social-auth-button more" data-form="moreSocial">
                        或者使用更多账号登录
                    </button>
                </div>
            </form>

            <!-- 邮箱注册表单 -->
            <form id="emailRegisterForm" class="auth-form">
                <div class="form-group">
                    <i class="ri-mail-line form-icon"></i>
                    <input type="email" id="regEmailAddress" placeholder="请输入邮箱" required>
                    <div class="error-message" id="regEmailError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="regEmailPassword" placeholder="请输入密码" required>
                    <div class="error-message" id="regEmailPasswordError"></div>
                </div>
                <div class="form-group">
                    <i class="ri-lock-line form-icon"></i>
                    <input type="password" id="regEmailConfirmPassword" placeholder="请确认密码" required>
                    <div class="error-message" id="regEmailConfirmPasswordError"></div>
                </div>
                <div class="form-group agreement">
                    <input type="checkbox" id="regEmailAgreement" required>
                    <label for="regEmailAgreement">已阅读并同意<span class="agreement-text">《用户协议》</span>和<span
                            class="agreement-text">《隐私协议》</span></label>
                </div>
                <button type="submit" class="auth-submit" id="regEmailSubmit">注册</button>
                <div class="auth-footer">
                    <a href="#" class="auth-link" data-form="email">返回登录</a>
                </div>
                <div class="social-login">
                    <div class="divider">
                        <span>或</span>
                    </div>
                    <button type="button" class="social-auth-button google">
                        <img src="images/social/google.png" alt="Google Icon">
                        使用Google账号登录
                    </button>
                    <button type="button" class="social-auth-button more" data-form="moreSocial">
                        或者使用更多账号登录
                    </button>
                </div>
            </form>

            <!-- 更多社交账号表单 -->
            <div id="moreSocialForm" class="auth-form social-form">
                <div class="social-form-header">
                    <button type="button" class="social-back-button" data-form="phone">
                        <i class="ri-arrow-left-line"></i>
                    </button>
                    <h2 class="auth-form-title">返回HaoSurge登录</h2>
                </div>
                <div class="social-options">
                    <button type="button" class="social-auth-button facebook">
                        <img src="images/social/facebook.png" alt="Facebook Icon">
                        使用Facebook账号登录
                    </button>
                    <button type="button" class="social-auth-button instagram">
                        <img src="images/social/instagram.png" alt="Instagram Icon">
                        使用Instagram账号登录
                    </button>
                    <button type="button" class="social-auth-button youtube">
                        <img src="images/social/youtube.png" alt="YouTube Icon">
                        使用YouTube账号登录
                    </button>
                    <button type="button" class="social-auth-button tiktok">
                        <img src="images/social/tiktok.png" alt="TikTok Icon">
                        使用TikTok账号登录
                    </button>
                    <button type="button" class="social-auth-button wechat">
                        <img src="images/social/wechat.png" alt="WeChat Icon">
                        使用WeChat账号登录
                    </button>
                    <button type="button" class="social-auth-button linkedin">
                        <img src="images/social/linkedin.png" alt="LinkedIn Icon">
                        使用LinkedIn账号登录
                    </button>
                    <button type="button" class="social-auth-button pinterest">
                        <img src="images/social/pinterest.png" alt="Pinterest Icon">
                        使用Pinterest账号登录
                    </button>
                    <button type="button" class="social-auth-button twitter">
                        <img src="images/social/twitter.png" alt="Twitter Icon">
                        使用Twitter账号登录
                    </button>
                    <button type="button" class="social-auth-button apple">
                        <img src="images/social/apple.png" alt="Apple Icon">
                        使用Apple账号登录
                    </button>
                    <button type="button" class="social-auth-button github">
                        <img src="images/social/github.png" alt="GitHub Icon">
                        使用GitHub账号登录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主界面同步滑动容器 -->
    <div class="sync-slide-container">
        <div class="logo-section">
            <img src="images/surge_logo.png" alt="Surge Logo" id="mainLogo">
        </div>
        <div class="search-container">
            <div class="search-bar">
                <button id="search-icon-button">
                    <i class="ri-search-line search-icon"></i>
                </button>
                <input type="text" id="search-input">
                <div class="search-icons">
                    <i class="ri-mic-line search-icon"></i>
                    <i class="ri-camera-line search-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>