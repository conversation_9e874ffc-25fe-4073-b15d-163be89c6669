@echo off
chcp 65001 >nul
echo ========================================
echo       HaoSurge 端口管理工具
echo ========================================
echo.

:menu
echo 请选择操作：
echo 1. 检查端口8080状态
echo 2. 停止端口8080上的所有进程
echo 3. 启动服务器（端口8080）
echo 4. 打开主页面
echo 5. 打开位置测试页面
echo 6. 打开功能验证页面
echo 7. 退出
echo.
set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" goto check_port
if "%choice%"=="2" goto stop_processes
if "%choice%"=="3" goto start_server
if "%choice%"=="4" goto open_main
if "%choice%"=="5" goto open_location_test
if "%choice%"=="6" goto open_function_test
if "%choice%"=="7" goto exit
echo 无效选择，请重新输入
goto menu

:check_port
echo.
echo 检查端口8080状态...
netstat -an | find "8080"
if %errorlevel% equ 0 (
    echo 端口8080正在使用中
) else (
    echo 端口8080空闲
)
echo.
pause
goto menu

:stop_processes
echo.
echo 停止端口8080上的所有进程...
for /f "tokens=5" %%a in ('netstat -ano ^| find "8080" ^| find "LISTENING"') do (
    echo 停止进程 %%a...
    taskkill /F /PID %%a >nul 2>&1
)
echo 完成
echo.
pause
goto menu

:start_server
echo.
echo 启动服务器...
call 启动项目.bat
goto menu

:open_main
echo.
echo 打开主页面...
start http://localhost:8080
goto menu

:open_location_test
echo.
echo 打开位置测试页面...
start http://localhost:8080/test-location.html
goto menu

:open_function_test
echo.
echo 打开功能验证页面...
start http://localhost:8080/功能验证.html
goto menu

:exit
echo.
echo 再见！
exit /b 0
